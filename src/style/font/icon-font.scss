@import "variable/icon-variable";

@font-face {
  font-family: '#{$cat-icon-font-family}';
  src: url('#{$cat-icon-font-path}/#{$cat-icon-font-family}.ttf?t20240229') format('truetype'),
  url('#{$cat-icon-font-path}/#{$cat-icon-font-family}.woff?20251020') format('woff'),
  url('#{$cat-icon-font-path}/#{$cat-icon-font-family}.svg?20240229##{$cat-icon-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$cat-icon-font-family}' !important;
  speak: never;
  font-style: normal;font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-fuel {
  &:before {
    content: $icon-fuel;
  }
}
.icon-equipment {
  &:before {
    content: $icon-equipment;
  }
}
.icon-offer {
  &:before {
    content: $icon-offer;
  }
}
.icon-diagnostics {
  &:before {
    content: $icon-diagnostics;
  }
}
.icon-redo-right {
  &:before {
    content: $icon-redo-right;
  }
}
.icon-sound {
  &:before {
    content: $icon-sound;
  }
}
.icon-repair {
  &:before {
    content: $icon-repair;
  }
}
.icon-connect-fail {
  &:before {
    content: $icon-connect-fail;
  }
}
.icon-camera {
  &:before {
    content: $icon-camera;
  }
}
.icon-location {
  &:before {
    content: $icon-location;
  }
}
.icon-clock {
  &:before {
    content: $icon-clock;
  }
}
.icon-search {
  &:before {
    content: $icon-search;
  }
}
.icon-back {
  &:before {
    content: $icon-back;
  }
  line-height: 1.2em;
}
.icon-chevron-down {
  &:before {
    content: $icon-chevron-down;
  }
}
.icon-chevron-up {
  &:before {
    content: $icon-chevron-up;
  }
}
.icon-chevron-right {
  &:before {
    content: $icon-chevron-right;
  }
}
.icon-contact {
  &:before {
    content: $icon-contact;
  }
}
.icon-contract {
  &:before {
    content: $icon-contract;
  }
}
.icon-hamburger {
  &:before {
    content: $icon-hamburger;
    color: #505050;
  }
}
.icon-language {
  &:before {
    content: $icon-language;
  }
}
.icon-message-success {
  &:before {
    content: $icon-message-success;
  }
}
.icon-notification-new {
  &:before {
    content: $icon-notification-new;
  }
}
.icon-notification {
  &:before {
    content: $icon-notification;
  }
}
.icon-plus {
  &:before {
    content: $icon-plus;
  }
}
.icon-success {
  &:before {
    content: $icon-success;
  }
}
.icon-x-bold {
  &:before {
    content: $icon-x-bold;
  }
}
.icon-x {
  &:before {
    content: $icon-x;
  }
}
.icon-direction {
  &:before {
    content: $icon-direction;
  }
}
.icon-whatsapp {
  &:before {
    content: $icon-whatsapp;
    color: #505050;
  }
}
.icon-edit {
  &:before {
    content: $icon-edit;
    color: #505050;
  }
}
.icon-headset {
  &:before {
    content: $icon-headset;
    color: #505050;
  }
}
.icon-padlock {
  &:before {
    content: $icon-padlock;
    color: #505050;
  }
}
.icon-phone-call {
  &:before {
    content: $icon-phone-call;
  }
}
.icon-boom-logo {
  &:before {
    content: $icon-boom-logo;
    color: #505050;
  }
}
.icon-pin {
  &:before {
    content: $icon-pin;
    color: #505050;
  }
}
.icon-download {
  &:before {
    content: $icon-download;
  }
}
.icon-exit {
  &:before {
    content: $icon-exit;
  }
}
.icon-settings {
  &:before {
    content: $icon-settings;
  }
}
.icon-cantact {
  &:before {
    content: $icon-cantact;
  }
}
.icon-phone {
  &:before {
    content: $icon-phone;
  }
}
.icon-filter {
  &:before {
    content: $icon-filter;
  }
}
.icon-wifi {
  &:before {
    content: $icon-wifi;
  }
}
.icon-spinner8 {
  &:before {
    content: $icon-spinner8;
  }
}
.icon-dot3 {
  &:before {
    content: $icon-dot3;
  }
}
.icon-coupon {
  &:before {
    content: $icon-coupon;
  }
}
.icon-campaign {
  &:before {
    content: $icon-campaign;
  }
}
.icon-discount {
  &:before {
    content: $icon-discount;
  }
}
.icon-tl {
  &:before {
    content: $icon-tl;
  }
}
.icon-usd {
  &:before {
    content: $icon-usd;
  }
}
.icon-euro {
  &:before {
    content: $icon-euro;
  }
}
.icon-ruble {
  &:before {
    content: $icon-ruble;
  }
}
.icon-tenge {
  &:before {
    content: $icon-tenge;
  }
}
.icon-manat {
  &:before {
    content: $icon-manat;
  }
}
.icon-lari {
  &:before {
    content: $icon-lari;
  }
}
.icon-close-circle {
  &:before {
    content: $icon-close-circle;
  }
}
.icon-videocall {
  &:before {
    content: $icon-videocall;
  }
}
.icon-star {
  &:before {
    content: $icon-star;
  }
}
.icon-persons {
  &:before {
    content: $icon-persons;
  }
}
.icon-eye {
  &:before {
    content: $icon-eye;
  }
}
.icon-calendar {
  &:before {
    content: $icon-calendar;
  }
}
.icon-company {
  &:before {
    content: $icon-company;
  }
}
.icon-ladybird {
  &:before {
    content: $icon-ladybird;
  }
}
.icon-shopping-cart {
  &:before {
    content: $icon-shopping-cart;
  }
}
.icon-ticket {
  &:before {
    content: $icon-ticket;
  }
}
.icon-ticket-bold {
  &:before {
    content: $icon-ticket-bold;
  }
}
.icon-file {
  &:before {
    content: $icon-file;
  }
}
.icon-generator {
  &:before {
    content: $icon-generator;
  }
}
.icon-marina {
  &:before {
    //content: $icon-marina;
    content: $icon-generator;
  }
}

.icon-trash {
  &:before {
    content: $icon-trash;
  }
}
.icon-sort {
  &:before {
    content: $icon-sort;
  }
}
.icon-delete {
  &:before {
    content: $icon-delete;
  }
}

