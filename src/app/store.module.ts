import { NgModule } from '@angular/core';
import { NgxsReduxDevtoolsPluginModule } from '@ngxs/devtools-plugin';
import { NgxsFormPluginModule } from '@ngxs/form-plugin';
import { NgxsStoragePluginModule } from '@ngxs/storage-plugin';
import { NgxsModule } from '@ngxs/store';
import { NgxsResetPluginModule } from 'ngxs-reset-plugin';

import { environment } from '../environments/environment';
import { LoginState } from './module/authentication/state/login/login.state';
import { DefinitionState } from './module/definition/state/definition.state';
import { FormState } from './module/form/state/form.state';
import { AppState } from './state/app/app.state';
import { CustomerState } from './module/customer/state/customer.state';
import { CommonState } from './module/shared/state/common/common.state';
import { InspectionState } from './module/expertise/state/inspection.state';
import { SettingsState } from './module/shared/state/settings/settings.state';
import { EquipmentState } from './module/customer/state/equipment.state';
import { OfferState } from './module/customer/state/offer.state';
import { BoomGuruState } from './module/boom-guru/state/boom-guru.state';
import { ServiceState } from './module/customer/state/service.state';
import { LoyalityState } from './module/customer/state/loyality.state';
import { VideocallMessageState } from './module/live-support/state/videocall-message.state';
import { VideocallState } from './module/live-support/state/videocall.state';
import { FinancialState } from './module/customer/state/financial.state';
import { DonateState } from './module/customer/state/donate.state';
import { SurveyState } from './module/survey/state/survey.state';
import { InstantSurveyState } from './module/instant-survey/state/instant-survey.state';

@NgModule({
  imports: [
    NgxsModule.forRoot([
      CustomerState,
      EquipmentState,
      OfferState,
      ServiceState,
      FinancialState,
      LoginState,
      DefinitionState,
      FormState,
      AppState,
      CommonState,
      SettingsState,
      InspectionState,
      LoyalityState,
      VideocallMessageState,
      VideocallState,
      DonateState,
      SurveyState,
      BoomGuruState,
      InstantSurveyState
    ], { developmentMode: !environment.production }),
    NgxsReduxDevtoolsPluginModule.forRoot({
      disabled: environment.production,
      name: 'Borusan Cat 7/24',
    }),
    NgxsFormPluginModule.forRoot(),
    // NgxsLoggerPluginModule.forRoot({
    //   disabled: environment.production,
    // }),
    NgxsStoragePluginModule.forRoot({
      key: !environment.production ? ['login', 'common.serviceWarningShowed'] : ['common.serviceWarningShowed'],
    }),
    NgxsResetPluginModule.forRoot(),
  ],
  exports: [NgxsModule],
})
export class StoreModule {}
