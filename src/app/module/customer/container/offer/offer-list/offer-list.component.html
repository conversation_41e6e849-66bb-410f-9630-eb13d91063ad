<cat-pull-to-refresh (refresh)="doRefresh()" [turnBack]="offerListLoading"
                     [className]="'offer-list-content'">
  <div class="pb-4 ">
    <!--  <div class="d-flex justify-content-center pt-2" *ngIf="groupedFilters?.All?.length">-->
    <!--    <div catUserClick [section]="'QUOTATIONS'" [subsection]="'REQUEST_EQUIPMENT_PART'" (click)="requestSpareParts()"-->
    <!--      class="equipmnet-part-btn btn btn-info btn-sm px-2 mr-2">-->
    <!--      {{ "_order_parts_btn" | translate }}-->
    <!--    </div>-->
    <!--    <div *ngIf="pssrList.length > 0" (click)="contactModal = true"-->
    <!--      class="cursor-pointer d-inline-flex phone-btn px-2 py-1 justify-content-center align-items-center">-->
    <!--      <div class="phone-button-text">-->
    <!--        <p><i class="text-white icon icon-phone cursor-pointer"></i></p>-->
    <!--      </div>-->
    <!--      <div class="ml-2 phone-button-text text-wrap text-left">-->
    <!--        <p>-->
    <!--          {{ "_contact_with_customer_service" | translate }}-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <i class="icon icon-search p-2 mx-1 d-flex align-items-center" *ngIf="enableSearchArea" (click)="enableSearchArea = !enableSearchArea"></i>-->
    <!--  </div>-->
    <div class="row mx-2 mt-2 mb-2" *ngIf="groupedFilters[filterValue]?.length">
      <div class="col">
        <button
          *ngIf="featureMyOngoingOffers"
          class="btn btn-sm w-100 ongoing-offer-btn"
          style="color: #787878; border: 1px solid #D9D9D9;"
          (click)="navigateToOngoingOffers()"

          catUserClick
          [section]="'QUOTATIONS'"
          [subsection]="'ONGOING_OFFER_CLICK'"
        >
          {{ '_my_ongoing_offers' | translate }}
        </button>

      </div>
      <div
        *ngIf="!enableSearchArea && featureMyOngoingOffers"
        class="col-2 d-flex align-items-center pl-0"
      >
        <i
          class="icon icon-filter"
          [ngClass]="{'active-icon-filter': offerFilterActive}"
          (click)="enableSearchArea = !enableSearchArea"
        ></i>
        <!-- Sorting Dropdown -->
        <div ngbDropdown class="ml-3 sort-dropdown">
          <i class="icon icon-sort" ngbDropdownToggle> </i>
          <div ngbDropdownMenu>
            <button class="dropdown-item d-flex justify-content-between align-items-center"
                    [class.active]="sortField === 'postingDate' && sortDirection === 'asc'"
                    (click)="onSortChange('postingDate', 'asc')">
              {{ '_creation_date_asc' | translate }}
              <i class="icon-arrow-up ml-2" *ngIf="sortField === 'postingDate' && sortDirection === 'asc'"></i>
            </button>
            <button class="dropdown-item d-flex justify-content-between align-items-center"
                    [class.active]="sortField === 'postingDate' && sortDirection === 'desc'"
                    (click)="onSortChange('postingDate', 'desc')">
              {{ '_creation_date_desc' | translate }}
              <i class="icon-arrow-down ml-2" *ngIf="sortField === 'postingDate' && sortDirection === 'desc'"></i>
            </button>
            <div class="dropdown-divider"></div>
            <button class="dropdown-item d-flex justify-content-between align-items-center"
                    [class.active]="sortField === 'quotationValidDate' && sortDirection === 'desc'"
                    (click)="onSortChange('quotationValidDate', 'desc')">
              {{ '_validity_date_desc' | translate }}
              <i class="icon-arrow-down ml-2"
                 *ngIf="sortField === 'quotationValidDate' && sortDirection === 'desc'"></i>
            </button>
            <button class="dropdown-item d-flex justify-content-between align-items-center"
                    [class.active]="sortField === 'quotationValidDate' && sortDirection === 'asc'"
                    (click)="onSortChange('quotationValidDate', 'asc')">
              {{ '_validity_date_asc' | translate }}
              <i class="icon-arrow-up ml-2" *ngIf="sortField === 'quotationValidDate' && sortDirection === 'asc'"></i>
            </button>
          </div>
        </div>
      </div>
    </div>


    <cat-basic-modal [(status)]="enableSearchArea">
      <form [formGroup]="form" (submit)="onFilterSubmit()" class="d-flex flex-column filter-search-modal mt-2">
        <div>
          <div class="search-area position-relative d-flex align-items-center">
            <input
              [placeholder]="'_offer_no' | translate"
              class="form-control search-input"
              type="tel"
              maxlength="10"
              (input)="onInputOffer($event)"
              inputmode="numeric"
              formControlName="search"
              placement="bottom"
              triggers="none"
            />
            <i class="icon icon-search"></i>
          </div>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.search) }"
            class="invalid-feedback pl-3"
          >
            {{ '_search_min_length_3' | translate }}
          </div>
        </div>
        <div>
          <div class="search-area position-relative d-flex align-items-center">
            <input
              [placeholder]="'_serial_number' | translate"
              class="form-control search-input"
              maxlength="10"
              formControlName="serialNumber"
              placement="bottom"
              triggers="none"
            />
            <i class="icon icon-search"></i>
          </div>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.serialNumber) }"
            class="invalid-feedback pl-3"
          >
            {{ '_search_min_length_3' | translate }}
          </div>
        </div>

        <div class="position-relative">
          <input
            type="date"
            class="form-control"
            name="date"
            id="Date"
            formControlName="date"
          />
          <span *ngIf="!form?.value?.date" class="position-absolute date-text">{{ '_date' | translate }}</span>
        </div>
        <!--        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1rem;">
                  <input
                    type="tel"
                    maxlength="20"
                    (input)="onInputOffer($event)"
                    class="form-control"
                    name="amount"
                    [placeholder]="'_amount' | translate"
                    formControlName="amount"
                  />
                  <ng-select
                  [searchable]="false"
                  [clearable]="false"
                  [dropdownPosition]="'top'"
                  formControlName="operator"
                  name="operator"
                >
                  <ng-option
                    *ngFor="let operator of filterOperators"
                    [value]="operator"
                  >
                    {{ operator }}
                  </ng-option>
                </ng-select>
                </div>-->

        <div class="m-0 mb-3">
          <button
            type="submit"
            class="btn btn-sm btn-warning text-white mb-3 w-100"
          >
            {{ "_filter" | translate }}
          </button>
          <button
            type="button"
            (click)="onClearFilter()"
            class="btn btn-sm btn-secondary col"
            [disabled]="
              !form.value.search &&
              !form.value.date &&
              !form.value.amount &&
              form.value.amount !== 0
            "
          >
            {{ "_clear" | translate }}
          </button>
        </div>

      </form>
    </cat-basic-modal>


    <!-- categories -->
    <div
      class="col-12 categories mx-auto mt-3 p-2"
      *ngIf="groupedFilters?.All?.length"
    >

      <div #tabContainer class="categories-container d-flex align-items-center justify-content-around">
        <ng-container>
          <div class="categories-content px-3">
            <div
              class="text-container"
              *ngIf="groupedFilters['All']?.length"
              [ngClass]="{ activeCategory: 'All' === filterValue }"
              (click)="setFilter('All')"
            >
              {{ "_all" | translate }}
            </div>
            <span *ngIf="'All' === filterValue" style="margin-top: 12px"></span>
          </div>
        </ng-container>
        <ng-container *ngFor="let category of offerCategoriesProcessType">
          <div class="categories-content px-3">
            <div
              class="text-container"
              *ngIf="groupedFilters[category.key]?.length"
              [ngClass]="{ activeCategory: category.key === filterValue }"
              (click)="setFilter(category.key)"
            >
              {{ category?.value | translate }}
            </div>
            <span
              *ngIf="category.key === filterValue"
              style="margin-top: 12px"
            ></span>
          </div>
        </ng-container>
      </div>
      <hr style="min-width: 100%" [style.width.px]="tabContainerWight"/>
    </div>
    <ng-container *ngIf="groupedFilters[filterValue]?.length; else emptyList">
      <div
        class="d-flex justify-content-center container pt-2"
        *ngIf="groupedFilters?.All?.length"
      >
        <div
          [hasPermission]="PermissionEnum.RequestsSparePart"
          catUserClick
          [section]="'QUOTATIONS'"
          [subsection]="'REQUEST_EQUIPMENT_PART'"
          (click)="createOffer(filterValue)"
          class="equipmnet-part-btn col btn btn-info btn-sm px-2 mr-2"
          *ngIf="filterValue !== OfferCategoriesEnum.ALL"
        >
          {{ getFilterValue(filterValue) | translate }}
        </div>
        <div
          ngbDropdown
          *ngIf="filterValue === OfferCategoriesEnum.ALL"
          class="equipmnet-part-btn col btn btn-info btn-sm px-2 mr-2"
        >
          <div
            role="group"
            aria-label="Button group with nested dropdown"
            ngbDropdownToggle
          >
            {{ "_create_request" | translate }}
            <div *ngIf="availableQuotationTypes" class="equipment-menu mt-1 dropdown-menu" ngbDropdownMenu>
              <button
                [hasPermission]="PermissionEnum.RequestsSparePart"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.SPARE_PART_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.SPARE_PART_OFFER)"
              >
                {{ "_order_parts_btn" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsService"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.SERVICE_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.SERVICE_OFFER)"
              >
                {{ "_create_service_request" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsMda"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.MDA_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.MDA_OFFER)"
              >
                {{ "_create_mda" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsRental"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.RENTAL)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.RENTAL)"
              >
                {{ "_create_rental_request" | translate }}
              </button>
              <button
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.NEW_MACHINE)"
                class="btn my-1"
                ngbDropdownItem
                (click)="goToCatalogNew()"
              >
                {{ "_open_to_catalog_page" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div
          *ngIf="pssrList.length > 0"
          (click)="onOpenModal()"
          class="cursor-pointer col d-inline-flex phone-btn px-2 py-1 justify-content-center align-items-center"
        >
          <div class="phone-button-text">
            <p><i class="text-white icon icon-phone cursor-pointer"></i></p>
          </div>
          <div class="ml-2 phone-button-text text-wrap text-left">
            <p>
              {{ "_contact_with_customer_service" | translate }}
            </p>
          </div>
        </div>
        <div
          [hasPermission]="PermissionEnum.VideoCallBanko"
          *ngIf="
            isShowDigitalBanko &&
            filterValue === OfferCategoriesEnum.SPARE_PART_OFFER
          "
          class="btn-group float-right ml-2"
          ngbDropdown
          role="group"
          aria-label="Button group with nested dropdown"
        >
          <button
            class="dropdown-after-none btn btn-sm dropdown-toggle-split service-menu-button py-0 px-1 border-0"
            ngbDropdownToggle
          >
            <i class="icon icon-dot3 font-weight-bold"></i>
          </button>
          <div class="equipment-menu mt-1 dropdown-menu" ngbDropdownMenu>
            <button
              *ngIf="isShowDigitalBanko && filterValue === 'Y101'"
              catUserClick
              [section]="'OFFER'"
              [subsection]="'START_VIDEOCALL'"
              class="btn my-1"
              ngbDropdownItem
              (click)="startDigitalBankoVideocall()"
            >
              <i class="icon icon-videocall pr-2"></i>
              {{ "_videocall_offer_list" | translate }}
            </button>
          </div>
        </div>

        <div
          *ngIf="!enableSearchArea && !featureMyOngoingOffers"
          class="p-2 d-flex align-items-center"
        >
          <i
            class="icon icon-filter"
            [ngClass]="{'active-icon-filter': offerFilterActive}"
            (click)="enableSearchArea = !enableSearchArea"
          ></i>
        </div>

      </div>
      <div
        class="px-3"
        *ngFor="
          let offer of groupedFilters[filterValue];
          last as last;
          first as first
        "
      >
        <div
          [id]="offer?.quotationNumber"
          class="py-4 px-3 bg-white rounded mb-4"
          [ngClass]="{
            'mt-4': first,
            'offer-animation':
              paramQuotationNumber === offer?.quotationNumber &&
              offer?.quotationNumber !== null
          }"
          (click)="animationClick(offer?.quotationNumber)"
        >
          <div class="d-flex flex-row flex-nowrap position-relative justify-content-between mb-2">
            <div
              class="h6 offer-title text-info mb-0 d-flex align-items-center"
              *ngIf="offer?.processTypeDescription"
            >
              {{ offer?.processTypeDescription }}
            </div>
            <!-- ? Offer Management Dropdown -->
            <div
              class="text-info offer-dropdown"
              *ngIf="
                (offer?.actions?.length &&
                  offerActionCheck(offer, OfferActionsEnum.CreateOrder)) ||
                (offerActionCheck(offer, OfferActionsEnum.Approve) &&
                  offerActionCheck(offer, OfferActionsEnum.Reject) &&
                  offerActionCheck(offer, OfferActionsEnum.Revision) &&
                  !(offer?.status === OfferStatusEnum.Rejected) &&
                  offer?.status !== OfferStatusEnum.IsProcessing)
              "
            >
              <div
                class="btn-group position-static float-right"
                [class.text-warning]="
                  offerActionCheck(offer, OfferActionsEnum.CreateOrder) ||
                  !(
                    offerActionCheck(offer, OfferActionsEnum.Approve) &&
                    offerActionCheck(offer, OfferActionsEnum.Reject) &&
                    offerActionCheck(offer, OfferActionsEnum.Revision)
                  )
                "
                ngbDropdown
                placement="bottom-right bottom-left bottom"
                role="group"
                aria-label="Button group with nested dropdown"
              >
                <button [hasPermission]="PermissionEnum.QuotationActions"
                        class="btn btn-outline position-static order offer-menu-button font-size-12px py-1 px-2 "
                        ngbDropdownToggle>
                  {{ "_approval_transactions" | translate }}
                  <i class="icon icon-chevron-down"></i>
                </button>
                <div class="offer-menu rounded border-0 py-0 mt-0 dropdown-menu" ngbDropdownMenu>
                  <button
                    *ngIf="
                      offerActionCheck(offer, OfferActionsEnum.CreateOrder)
                    "
                    class="p-3 active"
                    catUserClick
                    [section]="'OFFER'"
                    [subsection]="'GO_TO_PAYMENT_PAGE'"
                    [data]="{ quotationNumber: offer?.quotationNumber }"
                    (click)="offerCreateOrder(offer)"
                    ngbDropdownItem
                  >
                    {{ "_offer_create_order" | translate }}
                  </button>
                  <button
                    *ngIf="offerActionCheck(offer, OfferActionsEnum.Approve)"
                    class="p-3 offer-menu-element approve-btn border-bottom"
                    (click)="offerApprovalBtnClick(offer)"
                    catUserClick
                    [section]="'OFFER'"
                    [subsection]="'OPEN_OFFER_APPROVE'"
                    [data]="{ quotationNumber: offer?.quotationNumber }"
                    ngbDropdownItem
                  >
                    <i class="icon icon-message-success pr-2"></i>
                    {{ "_offer_approval" | translate }}
                  </button>
                  <button
                    *ngIf="offerActionCheck(offer, OfferActionsEnum.Revision)"
                    class="p-3 offer-menu-element revision-btn border-bottom"
                    (click)="offerRevisionBtnClick(offer)"
                    catUserClick
                    [section]="'OFFER'"
                    [subsection]="'OFFER_REVISION_CLICK'"
                    [data]="{ quotationNumber: offer?.quotationNumber }"
                    ngbDropdownItem
                  >
                    <i class="icon icon-edit pr-2"></i>
                    {{ "_offer_revision" | translate }}
                  </button>
                  <button
                    *ngIf="offerActionCheck(offer, OfferActionsEnum.Reject)"
                    class="p-3 offer-menu-element reject-btn"
                    (click)="offerRejectBtnClick(offer)"
                    catUserClick
                    [section]="'OFFER'"
                    [subsection]="'OFFER_REJECT_CLICK'"
                    [data]="{ quotationNumber: offer?.quotationNumber }"
                    ngbDropdownItem
                  >
                    <i class="icon icon-close-circle pr-2"></i>
                    {{ "_offer_reject" | translate }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="offer-text">
              <div class="row">
                <div class="text-nowrap col">{{ "_offer_no" | translate }}</div>
                <div class="pr-1 col">
                  <span class="mr-3">:</span>
                  {{ offer?.quotationNumber }}
                </div>
              </div>
              <!-- TODO enable for sorting visibility <div
                *ngIf="
                  offer?.postingDate
                "
                class="mt-1 row"
              >
                <div class="text-nowrap col">
                  {{ "_creation_date" | translate }}
                </div>
                <div class="pr-1 col">
                  <span class="mr-3">:</span>
                  {{ offer?.postingDate | date : "dd.MM.yyyy" }}
                </div>
              </div>-->
              <div
                *ngIf="
                  offer?.quotationValidDate &&
                  offer?.processType !== OfferCategoriesEnum.CVA_OFFER
                "
                class="mt-1 row"
              >
                <div class="text-nowrap col">
                  {{ "_validity_date" | translate }}
                </div>
                <div class="pr-1 col">
                  <span class="mr-3">:</span>
                  {{ offer?.quotationValidDate | date : "dd.MM.yyyy" }}
                </div>
              </div>
              <div
                class="mt-1 row"
                *ngIf="
                  ![
                    OfferCategoriesEnum.NEW_MACHINE,
                    OfferCategoriesEnum.SERVICE_OFFER
                  ].includes(offer?.processType)
                "
              >
                <div class="text-nowrap col">
                  {{ "_total_amount" | translate }}
                </div>
                <div class="pr-1 col">
                  <span class="mr-3">:</span>
                  {{ offer?.totalAmount | number : "" : locale }}
                  {{ offer?.currency }}
                </div>
              </div>
              <div
                class="mt-1 row"
                *ngIf="
                  [
                    OfferCategoriesEnum.NEW_MACHINE,
                    OfferCategoriesEnum.SERVICE_OFFER
                  ].includes(offer?.processType)
                "
              >
                <div class="text-nowrap col">
                  {{ "_total_amount" | translate }}
                </div>
                <div class="pr-1 col" *ngIf="offer?.totalAmount !== 0">
                  <span class="mr-3">:</span>
                  {{ offer?.totalAmount | number : "" : locale }}
                  {{ offer?.currency }}
                </div>
                <span class="pr-1 col" *ngIf="offer?.totalAmount === 0">
                  <span class="mr-3">:</span>
                  {{ offer?.netAmount | number : "" : locale }}
                  {{ offer?.currency }} + {{ "_kdv" | translate }}
                </span>
              </div>
              <div *ngIf="offer?.status" class="mt-1 row">
                <div class="text-nowrap col">
                  {{ "_approved_status" | translate }}
                </div>
                <div class="pr-1 col d-flex">
                  <span class="mr-3">:</span>
                  <div>{{ "_offer_" + (offer?.status | lowercase) | translate }}</div>
                </div>
              </div>
              <div *ngIf="offer?.paymentStatus" class="mt-1 row">
                <div class="text-nowrap col">
                  {{ "_payment_status" | translate }}
                </div>
                <span class="pr-1 col d-flex">
                  <span class="mr-3">:</span>
                  <div>
                    {{ offer?.paymentStatus }}
                  </div>
                </span>
              </div>
            </div>
            <div [ngClass]="{'spinner': isDownloading(offer?.quotationNumber)}">

              <div catUserClick [section]="'OFFER'" [subsection]="'OFFER_DOWNLOAD'"
                   [data]="{ quotationNumber: offer?.quotationNumber }"
                   catDownloadFile [downloadType]="DownloadTypeEnum.offer"
                   [downloadParams]="{ quotationNumber: offer?.quotationNumber, guid: offer?.guid }"
                   (downloadLoading)="downloading($event)"
                   class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                   [ngClass]="{'spinner': isDownloading(offer?.quotationNumber)}"
                   [id]="'downloadButton_' + offer?.quotationNumber">
                <i class="icon icon-spinner8" *ngIf="isDownloading(offer?.quotationNumber)"></i>
                <i class="icon icon-download" *ngIf="!isDownloading(offer?.quotationNumber)"></i>
                <a class="d-none" [download]="offer?.quotationNumber"></a>
              </div>
            </div>
          </div>

          <div
            *ngIf="
              offer?.orderGuid &&
              offer?.processType === OfferCategoriesEnum.RENTAL
            "
            (click)="selectAgreement(offer)"
            class="d-flex flex-row justify-content-center mt-1"
          >
            <button class="btn btn-sm btn-info text-white px-4" type="button">
              {{ "_agreement_detail" | translate }}
            </button>
          </div>
        </div>
      </div>
    </ng-container>
  </div>

  <ng-template #emptyList>
    <div class="text-center" *ngIf="!offerListLoading">
      <cat-empty-content [iconName]="'offer'" [message]="'_offer_list_empty'">
        <a *ngIf="offerFilterActive" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm mb-2"
           (click)="onClearFilter()">
          {{ "_clear_filters" | translate }}
        </a>
        <button
          *ngIf="featureMyOngoingOffers"
          class="btn btn-sm w-100 ongoing-offer-btn mb-2"
          style="color: #787878; border: 1px solid #D9D9D9;"
          (click)="navigateToOngoingOffers()"

          catUserClick
          [section]="'QUOTATIONS'"
          [subsection]="'ONGOING_OFFER_CLICK'"
        >
          {{ '_my_ongoing_offers' | translate }}
        </button>
        <div
          ngbDropdown
          class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm"
        >
          <div
            role="group"
            aria-label="Button group with nested dropdown"
            ngbDropdownToggle
          >
            {{ "_create_request" | translate }}
            <div *ngIf="availableQuotationTypes" class="equipment-menu mt-1 dropdown-menu" ngbDropdownMenu>
              <button
                [hasPermission]="PermissionEnum.RequestsSparePart"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.SPARE_PART_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.SPARE_PART_OFFER)"
              >
                {{ "_order_parts_btn" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsService"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.SERVICE_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.SERVICE_OFFER)"
              >
                {{ "_create_service_request" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsMda"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.MDA_OFFER)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.MDA_OFFER)"
              >
                {{ "_create_mda" | translate }}
              </button>
              <button
                [hasPermission]="PermissionEnum.RequestsRental"
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.RENTAL)"
                class="btn my-1"
                ngbDropdownItem
                (click)="createOffer(OfferCategoriesEnum.RENTAL)"
              >
                {{ "_create_rental_request" | translate }}
              </button>
              <button
                *ngIf="showAvailableQuotation(OfferCategoriesEnum.NEW_MACHINE)"
                class="btn my-1"
                ngbDropdownItem
                (click)="goToCatalogNew()"
              >
                {{ "_open_to_catalog_page" | translate }}
              </button>
            </div>
          </div>
        </div>
        <!-- <button catUserClick [section]="'QUOTATIONS'" [subsection]="'REQUEST_EQUIPMENT_PART'" -->
        <!-- (click)="createOffer(filterValue)" -->
        <!-- class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm"> -->
        <!-- {{ "_order_parts_btn" | translate }} -->
        <!-- </button> -->
        <div
          *ngIf="pssrList.length > 0"
          (click)="contactModal = true"
          class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm"
        >
          {{ "_contact_with_customer_service" | translate }}
        </div>
      </cat-empty-content>
    </div>
  </ng-template>
  <!-- ? Contact Modal -->
  <cat-contact-your-representative-modal
    [contactModal]="contactModal"
    (handleClose)="handleCloseRepresentativeModal()">
  </cat-contact-your-representative-modal>

  <!-- ? Offer Approval Modal -->
  <cat-basic-modal
    *ngIf="offerApprovalModal && selectedOffer"
    [(status)]="offerApprovalModal"
    [headerText]="'_offer_approval' | translate"
  >
    <div class="mb-3">
      <div
        class="d-flex justify-content-between align-items-center card m-2 p-2"
      >
        <div class="offer-textapproval">
          <div class="mr-2 pr-1 d-flex flex-fill text-wrap">
            <div class="mb-1 text-nowrap">{{ "_offer_no" | translate }}</div>
            <span class="mr-2 pr-1">:</span>{{ selectedOffer?.quotationNumber }}
          </div>
          <div>
            <div
              class="mr-2 pr-1 d-flex flex-fill"
              *ngIf="
                selectedOffer?.processType !== OfferCategoriesEnum.CVA_OFFER
              "
            >
              <div class="mb-1">{{ "_validity_date" | translate }}</div>
              <span class="mr-2 pr-1">:</span
              >{{ selectedOffer?.quotationValidDate | date : "dd.MM.yyyy" }}
            </div>
          </div>
        </div>
      </div>
      <div class="text-center">{{ "_offer_approval_text" | translate }}</div>
      <div class="mx-auto text-center mt-4">
        <button
          class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow col-8 mr-3"
          catUserClick
          [section]="'OFFER'"
          [subsection]="'OFFER_APPROVED_CLICK'"
          [data]="{ quotationNumber: selectedOffer?.quotationNumber }"
          (click)="offerApprovedClick()"
        >
          {{ "_offer_approval" | translate }}
        </button>
      </div>
    </div>
  </cat-basic-modal>
  <!-- ? Offer Revision Modal -->
  <cat-basic-modal
    *ngIf="offerRevisionModal && selectedOffer"
    (statusChange)="offerRevisionModalStatus()"
    [(status)]="offerRevisionModal"
    [headerText]="'_offer_revision_request' | translate"
  >
    <div class="mb-3">
      <div *ngIf="!revisionFormSendStatus" class="px-4 pb-5">
        <div
          class="d-flex justify-content-between align-items-center card m-2 p-2"
        >
          <div class="offer-textrevision">
            <div class="mr-2 pr-1 d-flex flex-fill">
              <div class="mb-1">{{ "_offer_no" | translate }}</div>
              <span class="mr-2 pr-1">:</span
              >{{ selectedOffer?.quotationNumber }}
            </div>
            <div>
              <div
                class="mr-2 pr-1 d-flex flex-fill"
                *ngIf="
                  selectedOffer?.processType !== OfferCategoriesEnum.CVA_OFFER
                "
              >
                <div class="mb-1">{{ "_validity_date" | translate }}</div>
                <span class="mr-2 pr-1">:</span
                >{{ selectedOffer?.quotationValidDate | date : "dd.MM.yyyy" }}
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mb-3">
          {{ "_offer_revision_text" | translate }}
        </div>
        <form (submit)="offerRevisionSend()" [formGroup]="revisionForm">
          <div class="form-group">
            <textarea
              catInputLength
              [name]="'Description'"
              [placeholder]="'_description' | translate"
              [rows]="5"
              class="form-control"
              formControlName="Description"
              minlength="3"
              maxlength="500"
              style="resize: none;"
            ></textarea>
            <div
              [ngClass]="{
                'd-block': isShowError(revisionForm.controls.Description)
              }"
              class="invalid-feedback pl-3"
            >
              {{
                getFormErrorMessage(revisionForm.controls.Description)
                  | translate
              }}
            </div>
          </div>

          <input
            [value]="'_send' | translate"
            [class.disabled]="revisionForm.invalid"
            [disabled]="
              borusanBlockedActions &&
              borusanBlockedActions?.indexOf(
                BorusanBlockedActionsEnum.SubmitRevisedQuotationForm
              ) >= 0
            "
            class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
            type="submit"
          />
        </form>
      </div>
    </div>
  </cat-basic-modal>
  <!-- ? Offer Reject Modal -->
  <cat-basic-modal
    *ngIf="offerRejectModal && selectedOffer"
    [(status)]="offerRejectModal"
    [headerText]="'_offer_reject_request' | translate"
  >
    <div class="mb-3">
      <div *ngIf="!rejectFormSendStatus" class="px-4 pb-5">
        <div
          class="d-flex justify-content-between align-items-center card m-2 p-2"
        >
          <div class="offer-textreject">
            <div class="mr-2 pr-1 d-flex flex-fill">
              <div class="mb-1">{{ "_offer_no" | translate }}</div>
              <span class="mr-2 pr-1">:</span
              >{{ selectedOffer?.quotationNumber }}
            </div>
            <div>
              <div
                class="mr-2 pr-1 d-flex flex-fill"
                *ngIf="
                  selectedOffer?.processType !== OfferCategoriesEnum.CVA_OFFER
                "
              >
                <div class="mb-1">{{ "_validity_date" | translate }}</div>
                <span class="mr-2 pr-1">:</span
                >{{ selectedOffer?.quotationValidDate | date : "dd.MM.yyyy" }}
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mb-3">
          {{ "_offer_reject_text" | translate }}
        </div>
        <div class="d-flex flex-row flex-nowrap mt-4">
          <button
            class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow col mr-3"
            catUserClick
            [section]="'OFFER'"
            [subsection]="'OFFER_REJECTED_CLICK'"
            [data]="{ quotationNumber: selectedOffer?.quotationNumber }"
            (click)="offerRejectedClick()"
          >
            {{ "_offer_reject" | translate }}
          </button>
          <button
            [disabled]="
              borusanBlockedActions &&
              borusanBlockedActions?.indexOf(
                BorusanBlockedActionsEnum.SubmitRejectedQuotationForm
              ) >= 0
            "
            class="modal-btn btn-sm btn btn-secondary btn-gradient text-white shadow col"
            catUserClick
            [section]="'OFFER'"
            [subsection]="'OFFER_REJECT_CANCEL_CLICK'"
            [data]="{ quotationNumber: selectedOffer?.quotationNumber }"
            (click)="offerModalClose('reject')"
          >
            {{ "_close" | translate }}
          </button>
        </div>
      </div>
    </div>
  </cat-basic-modal>
  <div *ngIf="revisionFormSendStatus" class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <div class="text-center px-4 py-5">
        <i class="icon icon-message-success d-inline-block mb-4"></i>
        <div class="success-message mb-5">
          {{ "_offer_successfully_revision_send_form" | translate }}
        </div>
        <div
          class="btn btn-warning btn-gradient btn-block text-white shadow"
          (click)="offerModalClose('revision')"
        >
          {{ "_close" | translate }}
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="rejectFormSendStatus" class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <div class="text-center px-4 py-5">
        <i class="icon icon-message-success d-inline-block mb-4"></i>
        <div class="success-message mb-5">
          {{ "_offer_successfully_reject_send_form" | translate }}
        </div>
        <div
          class="btn btn-warning btn-gradient btn-block text-white shadow"
          (click)="offerModalClose('reject')"
        >
          {{ "_close" | translate }}
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="approvalFormSendStatus" class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <div class="text-center px-4 py-5">
        <i class="icon icon-message-success d-inline-block mb-4"></i>
        <div class="success-message mb-5">
          {{ "_offer_successfully_approval_send_form" | translate }}
        </div>
        <div
          class="btn btn-warning btn-gradient btn-block text-white shadow"
          (click)="offerModalClose('approval')"
        >
          {{ "_close" | translate }}
        </div>
      </div>
    </div>
  </div>

  <div
    *ngIf="
      (isSparePartModule | async) && filterValue === OfferCategoriesEnum.SPARE_PART_OFFER
    "
    [hasPermission]="PermissionEnum.Ecommerce"
    class="spare-order-detail-text-bottom"
    (click)="openSparePartOrderDetail()">
    <!-- <div class="yellow-bar"></div> -->
    <span
      [innerHTML]="'_spare_part_offer_order_detail' | translate | safe : 'html'"
    ></span>
  </div>
  <cat-loader [show]="offerListLoading"></cat-loader>

  <cat-basic-modal
    *ngIf="agreementModal && selectedAgreement !== null"
    [(status)]="agreementModal"
    [headerText]="'_agreement_detail' | translate"
  >
    <hr/>
    <div class="d-flex justify-content-between pb-3">
      <div>
        <div
          class="mr-2 pr-1 d-flex flex-fill my-2"
          *ngIf="selectedAgreement?.orderStatus"
        >
          <div class="mb-1 text-break">{{ "_order_status" | translate }}</div>
          <span class="mr-1 pr-1">:</span>
          <span class="text-break w-75">
            {{ selectedAgreement?.orderStatusText | translate }}
          </span>
        </div>
        <div
          class="mr-1 pr-1 d-flex flex-fill"
          *ngIf="selectedAgreement?.orderPostingDate"
        >
          <div class="mb-1 text-break">
            {{ "_order_posting_date" | translate }}
          </div>
          <span class="mr-1 pr-1">:</span
          >{{ selectedAgreement?.orderPostingDate | date : "dd.MM.yyyy" }}
        </div>
      </div>
      <div class="d-flex justify-content-center align-items-center ml-2">
        <div
          catDownloadFile
          [downloadType]="DownloadTypeEnum.offer"
          [downloadParams]="{
            quotationNumber: selectedAgreement?.quotationNumber,
            guid: selectedAgreement?.orderGuid
          }"
          (downloadLoading)="downloading($event)"
          class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
          [class.spinner]="isDownloading(selectedAgreement?.quotationNumber)"
        >
          <i
            class="icon icon-spinner8"
            *ngIf="isDownloading(selectedAgreement?.quotationNumber)"
          ></i>
          <i
            class="icon icon-download"
            *ngIf="!isDownloading(selectedAgreement?.quotationNumber)"
          ></i>
          <a class="d-none" [download]="selectedAgreement?.quotationNumber"></a>
        </div>
      </div>
    </div>
    <hr/>
  </cat-basic-modal>
</cat-pull-to-refresh>
<cat-pull2refresh-animation></cat-pull2refresh-animation>
