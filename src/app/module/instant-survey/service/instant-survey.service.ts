import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import { 
  InstantSurveyModel, 
  InstantSurveyListRequest, 
  InstantSurveySubmitRequest 
} from '../model/instant-survey.model';

@Injectable({
  providedIn: 'root'
})
export class InstantSurveyService {

  constructor(
    private readonly http: HttpClient
  ) { }

  public getList(): Observable<InstantSurveyModel[]> {
    const body: InstantSurveyListRequest = {
      Position: "INSTANT_SURVEY_MAIN",
      GetDetails: true
    };

    return this.http.post<HttpResponse<InstantSurveyModel[]>>(`${environment.api}/survey/instantsurvey`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public submitSurvey(body: InstantSurveySubmitRequest): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/survey/answerinstantsurvey`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
}
