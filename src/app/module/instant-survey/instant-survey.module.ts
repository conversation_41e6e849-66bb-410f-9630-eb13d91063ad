import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';

import { InstantSurveyRoutingModule } from './instant-survey-routing.module';
import { InstantSurveyLayoutComponent } from './component/instant-survey-layout/instant-survey-layout.component';
import { InstantSurveyListComponent } from './component/instant-survey-list/instant-survey-list.component';
import { InstantSurveyDetailComponent } from './component/instant-survey-detail/instant-survey-detail.component';
import { InstantSurveyService } from './service/instant-survey.service';

@NgModule({
  declarations: [
    InstantSurveyLayoutComponent,
    InstantSurveyListComponent,
    InstantSurveyDetailComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    UserEventLogModule,
    InstantSurveyRoutingModule,
  ],
  providers: [InstantSurveyService]
})
export class InstantSurveyModule {}
