export interface InstantSurveyModel {
  surveyId: string;
  type: number;
  code: string;
  name: string;
  description: string;
  targetAudience: number;
  isActive: boolean;
  greetings: string;
  isAuth: boolean;
  assignedCompanies: any;
  assignedCountries: any;
  assignedUsers: any;
  responseText: string;
  surveyQuestions: InstantSurveyQuestion[];
}

export interface InstantSurveyQuestion {
  surveyId: string;
  questionId: string;
  question: string;
  order: number;
  isActive: boolean;
  questionType: number;
  questionTypeDesc: string;
  isRequired: boolean;
  hasOptions: boolean;
  isCommentBoxEnabled: boolean;
  isCommentBoxRequired: boolean;
  commentBoxMaxLength: number;
  conditionalQuestionId: string | null;
  conditionalQuestionValues: any;
  questionOptions: InstantSurveyQuestionOption[];
}

export interface InstantSurveyQuestionOption {
  surveyId: string;
  surveyQuestionId: string;
  text: string;
  value: string;
  order: number;
  isCorrectAnswer: boolean;
  boomCoinValue: number;
  boomCoinEvent: string | null;
  boomCoinSubEvent: string | null;
  dateCreated: string | null;
  createdBy: string | null;
  dateUpdated: string | null;
  updatedBy: string | null;
  id: string;
}

export interface InstantSurveyListRequest {
  Position: string;
  GetDetails: boolean;
}

export interface InstantSurveySubmitRequest {
  SurveyId: string;
  SurveyPosition: string;
  AdditionalData: any;
  RelatedContextId: any;
  Answers: InstantSurveyAnswer[];
}

export interface InstantSurveyAnswer {
  QuestionId: string;
  Answer: string;
  Comment: string | null;
}
