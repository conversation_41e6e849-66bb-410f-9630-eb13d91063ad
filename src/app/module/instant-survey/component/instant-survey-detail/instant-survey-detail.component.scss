.instant-survey-detail {
  padding-bottom: 80px; // Space for fixed bottom buttons

  // Navigation Header
  .nav-back {
    .icon-back {
      cursor: pointer;
      font-size: 1.2rem;
      color: #007bff;

      &:hover {
        color: #0056b3;
      }
    }
  }

  // VisionLink Banner
  .visionlink-banner {
    .card {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3) !important;
      }
    }

    .visionlink-icon {
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .cat-app-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }

    .text-dark-50 {
      color: white !important;
      opacity: 0.9;
    }

    h6 {
      color: white !important;
    }

    .btn-dark {
      border: none;
      font-weight: 600;
      transition: all 0.2s ease;
      border-radius: 6px;

      &:hover {
        background-color: #343a40;
        transform: scale(1.05);
      }
    }
  }

  .question-container {
    .card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      // Removed box-shadow for clean flat design

      .question-number {
        width: 32px;
        height: 32px;
        font-size: 14px;
        font-weight: 600;
        background-color: #6c757d;
        flex-shrink: 0;
      }

      .card-title {
        color: #333;
        font-weight: 500;
      }

      .form-check {
        margin-bottom: 0.75rem;

        .form-check-input {
          margin-top: 0.25rem;
        }

        .form-check-label {
          margin-left: 0.5rem;
          cursor: pointer;
        }
      }

      .form-control {
        border: 1px solid #ced4da;
        border-radius: 4px;

        &:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  .btn-link {
    color: #007bff;
    text-decoration: none;

    &:hover {
      color: #0056b3;
    }

    i {
      font-size: 1.2rem;
    }
  }

  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }

  .text-danger {
    font-size: 0.875rem;
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  // Fixed Bottom Buttons
  .fixed-bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    // Removed box-shadow and border-top for clean flat design

    .btn {
      min-width: 120px;
      font-weight: 600;
      border-radius: 6px;
    }

    .btn-warning {
      //background-color: #ffc107;
      //border-color: #ffc107;
      color: white;
      border-radius: 6px;

      &:hover {
        //background-color: #e0a800;
        //border-color: #d39e00;
        color: white;
      }

      &:disabled {
        //background-color: #ffc107;
        //border-color: #ffc107;
        color: white;
        opacity: 0.6;
      }
    }

    .btn-secondary {
      border-radius: 6px;
    }
  }

  // Survey Success Modal Content
  .survey-success-content {
    .success-icon {
      i {
        font-size: 4rem;

        &.icon-message-success {
          color: #28a745; // Green for success

          &.text-warning {
            color: #ffc107 !important; // Orange for incorrect answers
          }
        }
      }
    }

    .success-message {
      .modal-body-message {
        color: #333;
        line-height: 1.6;
        font-size: 1rem;
        margin: 0;
      }
    }

    .survey-statistics {
      .stats-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 0 auto;
        max-width: 300px;
      }

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 1.5rem;
          font-weight: 700;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.875rem;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }

    .btn-warning {
      background-color: #ffc107;
      border-color: #ffc107;
      color: white !important;
      border-radius: 6px;
      font-weight: 600;
      min-width: 140px;
      padding: 0.75rem 2rem;

      &:hover {
        background-color: #e0a800;
        border-color: #d39e00;
        color: white !important;
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        color: white !important;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 576px) {
    .question-number {
      width: 28px !important;
      height: 28px !important;
      font-size: 12px !important;
    }

    .fixed-bottom-buttons {
      .btn {
        min-width: 100px;
        font-size: 14px;
      }
    }

    .survey-success-content {
      .success-icon i {
        font-size: 3rem;
      }

      .survey-statistics {
        .stats-container {
          padding: 1rem;
          max-width: 280px;
        }

        .stat-number {
          font-size: 1.25rem !important;
        }

        .stat-label {
          font-size: 0.8rem !important;
        }
      }

      .btn-warning {
        min-width: 120px;
        padding: 0.6rem 1.5rem;
      }
    }
  }
}
