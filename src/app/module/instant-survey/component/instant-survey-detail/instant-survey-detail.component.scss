.instant-survey-detail {
  .question-container {
    .card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .card-title {
        color: #333;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .form-check {
        margin-bottom: 0.75rem;

        .form-check-input {
          margin-top: 0.25rem;
        }

        .form-check-label {
          margin-left: 0.5rem;
          cursor: pointer;
        }
      }

      .form-control {
        border: 1px solid #ced4da;
        border-radius: 4px;

        &:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  .btn-link {
    color: #007bff;
    text-decoration: none;

    &:hover {
      color: #0056b3;
    }

    i {
      font-size: 1.2rem;
    }
  }

  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }

  .text-danger {
    font-size: 0.875rem;
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}
