<div class="instant-survey-detail p-3" *ngIf="activeSurvey">
  <div class="d-flex align-items-center mb-3">
    <button class="btn btn-link p-0 mr-3" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
    </button>
    <h4 class="mb-0">{{ activeSurvey.name }}</h4>
  </div>

  <div class="survey-description mb-4" *ngIf="activeSurvey.description">
    <p class="text-muted">{{ activeSurvey.description }}</p>
  </div>

  <div class="survey-greetings mb-4" *ngIf="activeSurvey.greetings">
    <div class="alert alert-info">
      {{ activeSurvey.greetings }}
    </div>
  </div>

  <form [formGroup]="surveyForm" (ngSubmit)="onSubmit()" *ngIf="surveyForm">
    <div class="question-container" *ngFor="let question of activeSurvey.surveyQuestions; let i = index">
      <div class="card mb-3">
        <div class="card-body">
          <h6 class="card-title">
            {{ i + 1 }}. {{ question.question }}
            <span class="text-danger" *ngIf="question.isRequired">*</span>
          </h6>

          <!-- Multiple Choice Questions -->
          <div *ngIf="question.hasOptions && question.questionOptions.length > 0">
            <div class="form-check" *ngFor="let option of question.questionOptions">
              <input 
                class="form-check-input" 
                type="radio" 
                [id]="'option_' + option.id"
                [formControlName]="question.questionId"
                [value]="option.value">
              <label class="form-check-label" [for]="'option_' + option.id">
                {{ option.text }}
              </label>
            </div>
          </div>

          <!-- Text Input for non-option questions -->
          <div *ngIf="!question.hasOptions">
            <textarea 
              class="form-control"
              [formControlName]="question.questionId"
              rows="3"
              [placeholder]="'_enter_your_answer' | translate">
            </textarea>
          </div>

          <!-- Comment Box -->
          <div *ngIf="question.isCommentBoxEnabled" class="mt-3">
            <label class="form-label">
              {{ "_additional_comments" | translate }}
              <span class="text-danger" *ngIf="question.isCommentBoxRequired">*</span>
            </label>
            <textarea 
              class="form-control"
              [formControlName]="question.questionId + '_comment'"
              rows="2"
              [maxlength]="question.commentBoxMaxLength || 500"
              [placeholder]="'_enter_comments' | translate">
            </textarea>
            <small class="form-text text-muted" *ngIf="question.commentBoxMaxLength">
              {{ "_max_characters" | translate }}: {{ question.commentBoxMaxLength }}
            </small>
          </div>

          <!-- Validation Error Messages -->
          <div class="text-danger mt-2" *ngIf="surveyForm.get(question.questionId)?.invalid && surveyForm.get(question.questionId)?.touched">
            <small>{{ "_this_field_is_required" | translate }}</small>
          </div>
          <div class="text-danger mt-2" *ngIf="question.isCommentBoxEnabled && surveyForm.get(question.questionId + '_comment')?.invalid && surveyForm.get(question.questionId + '_comment')?.touched">
            <small>{{ "_comment_is_required" | translate }}</small>
          </div>
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-between mt-4">
      <button type="button" class="btn btn-secondary" (click)="goBack()">
        {{ "_cancel" | translate }}
      </button>
      <button 
        type="submit" 
        class="btn btn-primary"
        [disabled]="!surveyForm.valid || (submitLoading$ | async)">
        <span *ngIf="submitLoading$ | async" class="spinner-border spinner-border-sm mr-2" role="status"></span>
        {{ "_submit_survey" | translate }}
      </button>
    </div>
  </form>

  <div *ngIf="!surveyForm" class="text-center py-4">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</div>

<div *ngIf="!activeSurvey" class="text-center py-4">
  <p class="text-muted">{{ "_survey_not_found" | translate }}</p>
  <button class="btn btn-primary" (click)="goBack()">
    {{ "_back_to_list" | translate }}
  </button>
</div>
