<div class="instant-survey-detail p-3" *ngIf="activeSurvey">
  <div class="h4 nav-back mb-3">
    <i class="icon icon-back mr-2" (click)="goBack()"></i>
    {{ activeSurvey.name }}
  </div>

  <div class="survey-description mb-4" *ngIf="activeSurvey.description">
    <p class="text-muted">{{ activeSurvey.description }}</p>
  </div>

  <div class="survey-greetings mb-4" *ngIf="activeSurvey.greetings">
    <div class="alert alert-info">
      {{ activeSurvey.greetings }}
    </div>
  </div>

  <!-- VisionLink Banner -->
  <div class="visionlink-banner mb-4">
    <div class="card border-0 shadow-sm bg-warning text-white">
      <div class="card-body p-3">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <div class="visionlink-icon mr-3">
              <img src="/assets/badges/cat-app.svg" alt="Cat® App" class="cat-app-icon"/>
            </div>
            <div>
              <h6 class="mb-1 font-weight-bold">Cat® VisionLink</h6>
              <small class="text-dark-50">{{ "_access_visionlink_platform" | translate }}</small>
            </div>
          </div>
          <button class="btn btn-dark btn-sm" (click)="openVisionLink()">
            <i class="fas fa-arrow-right mr-1"></i>
            {{ "_open" | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <form [formGroup]="surveyForm" (ngSubmit)="onSubmit()" *ngIf="surveyForm">
    <div class="question-container" *ngFor="let question of activeSurvey.surveyQuestions; let i = index">
      <div class="card mb-3">
        <div class="card-body">
          <div class="d-flex align-items-start mb-3">
            <div
              class="question-number bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mr-3">
              {{ i + 1 }}
            </div>
            <h6 class="card-title mb-0 flex-grow-1">
              {{ question.question }}
              <span class="text-danger" *ngIf="question.isRequired">*</span>
            </h6>
          </div>

          <!-- Multiple Choice Questions -->
          <div *ngIf="question.hasOptions && question.questionOptions.length > 0">
            <div class="form-check" *ngFor="let option of question.questionOptions">
              <input
                class="form-check-input"
                type="radio"
                [id]="'option_' + option.id"
                [formControlName]="question.questionId"
                [value]="option.value">
              <label class="form-check-label" [for]="'option_' + option.id">
                {{ option.text }}
              </label>
            </div>
          </div>

          <!-- Text Input for non-option questions -->
          <div *ngIf="!question.hasOptions">
            <textarea
              class="form-control"
              [formControlName]="question.questionId"
              rows="3"
              [placeholder]="'_enter_your_answer' | translate">
            </textarea>
          </div>

          <!-- Comment Box -->
          <div *ngIf="question.isCommentBoxEnabled" class="mt-3">
            <label class="form-label">
              {{ "_additional_comments" | translate }}
              <span class="text-danger" *ngIf="question.isCommentBoxRequired">*</span>
            </label>
            <textarea
              class="form-control"
              [formControlName]="question.questionId + '_comment'"
              rows="2"
              [maxlength]="question.commentBoxMaxLength || 500"
              [placeholder]="'_enter_comments' | translate">
            </textarea>
            <small class="form-text text-muted" *ngIf="question.commentBoxMaxLength">
              {{ "_max_characters" | translate }}: {{ question.commentBoxMaxLength }}
            </small>
          </div>

          <!-- Validation Error Messages -->
          <div class="text-danger mt-2"
               *ngIf="surveyForm.get(question.questionId)?.invalid && surveyForm.get(question.questionId)?.touched">
            <small>{{ "_this_field_is_required" | translate }}</small>
          </div>
          <div class="text-danger mt-2"
               *ngIf="question.isCommentBoxEnabled && surveyForm.get(question.questionId + '_comment')?.invalid && surveyForm.get(question.questionId + '_comment')?.touched">
            <small>{{ "_comment_is_required" | translate }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom padding to prevent content overlap with fixed buttons -->
    <div class="pb-5 mb-4"></div>
  </form>

  <!-- Fixed Bottom Button Container -->
  <div class="fixed-bottom-buttons bg-white p-3">
    <div class="d-flex justify-content-between">
      <button type="button" class="btn btn-secondary" (click)="goBack()">
        {{ "_cancel" | translate }}
      </button>
      <button
        type="submit"
        class="btn btn-warning"
        [disabled]="!surveyForm?.valid || (submitLoading$ | async)"
        (click)="onSubmit()">
        <cat-loader [show]="submitLoading$ | async"></cat-loader>
        {{ "_submit_survey" | translate }}
      </button>
    </div>
  </div>
  <div *ngIf="!surveyForm" class="text-center py-4">
    <cat-loader></cat-loader>
  </div>
</div>

<div *ngIf="!activeSurvey" class="text-center py-4">
  <p class="text-muted">{{ "_survey_not_found" | translate }}</p>
  <button class="btn btn-warning" (click)="goBack()">
    {{ "_back_to_list" | translate }}
  </button>
</div>

<!-- Success Modal -->
<cat-big-modal [(status)]="showSuccessModal">
  <div class="survey-success-content text-center py-3">
    <!-- Success/Warning Icon -->
    <div class="success-icon mb-4">
      <i [class]="getModalIconClass()" class="d-inline-block"></i>
    </div>

    <!-- Success Message -->
    <div class=" mb-4">
      <p class="modal-body-message">{{ successMessage }}</p>
    </div>

    <!-- Statistics Section -->
    <div class="survey-statistics mb-4" *ngIf="submitResponse$ | async as response">
      <div class="stats-container">
        <div class="row">
          <div class="col-6">
            <div class="stat-item">
              <div class="stat-number text-success">{{ response.correctAnswers }}</div>
              <div class="stat-label">{{ "_correct_answers" | translate }}</div>
            </div>
          </div>
          <div class="col-6">
            <div class="stat-item">
              <div class="stat-number text-danger">{{ response.wrongAnswers }}</div>
              <div class="stat-label">{{ "_wrong_answers" | translate }}</div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-6">
            <div class="stat-item">
              <div class="stat-number text-primary">{{ response.totalQuestions }}</div>
              <div class="stat-label">{{ "_total_questions" | translate }}</div>
            </div>
          </div>
          <div class="col-6">
            <div class="stat-item">
              <div class="stat-number text-warning">{{ response.gainedBoomCoin }}</div>
              <div class="stat-label">{{ "_gained_points" | translate }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Continue Button -->
    <button type="button" class="btn btn-warning text-white px-5 rounded-lg" (click)="closeSuccessModal()">
      {{ "_continue" | translate }}
    </button>
  </div>
</cat-big-modal>
