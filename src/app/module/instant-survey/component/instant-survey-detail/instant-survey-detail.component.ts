import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, of } from 'rxjs';
import {
  InstantSurveyAnswer,
  InstantSurveyModel,
  InstantSurveySubmitRequest,
  InstantSurveySubmitResponse
} from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { GetInstantSurveysAction, SubmitInstantSurveyAction } from '../../action/instant-survey.actions';
import { environment } from '../../../../../environments/environment';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';
import { TranslateService } from '@ngx-translate/core';
import { map, switchMap } from 'rxjs/operators';

@Component({
  selector: 'cat-instant-survey-detail',
  templateUrl: './instant-survey-detail.component.html',
  styleUrls: ['./instant-survey-detail.component.scss']
})
export class InstantSurveyDetailComponent implements OnInit {
  @Select(InstantSurveyState.activeSurvey)
  activeSurvey$: Observable<InstantSurveyModel>;

  @Select(InstantSurveyState.submitLoading)
  submitLoading$: Observable<boolean>;

  @Select(InstantSurveyState.submitResponse)
  submitResponse$: Observable<InstantSurveySubmitResponse>;

  surveyForm: FormGroup;
  activeSurvey: InstantSurveyModel;
  surveyId: string;
  showSuccessModal = false;
  successMessage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store,
    private fb: FormBuilder,
    private frameService: MessageFrameService,
    private translateService: TranslateService
  ) { }

  ngOnInit() {
    this.surveyId = this.route.snapshot.paramMap.get('id');
    this.activeSurvey$
      .pipe(switchMap(survey => {
        if (!survey) {
          return this.store.dispatch(new GetInstantSurveysAction());
        }
        return of(survey);
      }))
      .pipe(map(() => this.store.selectSnapshot(InstantSurveyState.surveyList)))
      .subscribe(surveys => {
        if (!surveys) {
          return;
        }
        this.activeSurvey = surveys.find(s => s.surveyId === this.surveyId);
        if (this.activeSurvey) {
          this.buildForm();

        }
      });
  }

  buildForm() {
    if (!this.activeSurvey || !this.activeSurvey.surveyQuestions) {
      return;
    }

    const formControls = {};

    this.activeSurvey.surveyQuestions.forEach(question => {
      const validators = question.isRequired ? [Validators.required] : [];
      formControls[question.questionId] = ['', validators];

      if (question.isCommentBoxEnabled) {
        const commentValidators = question.isCommentBoxRequired ? [Validators.required] : [];
        formControls[`${question.questionId}_comment`] = ['', commentValidators];
      }
    });

    this.surveyForm = this.fb.group(formControls);
  }

  onSubmit() {
    if (this.surveyForm.valid) {
      const answers: InstantSurveyAnswer[] = [];

      this.activeSurvey.surveyQuestions.forEach(question => {
        const answer = this.surveyForm.get(question.questionId)?.value;
        const comment = this.surveyForm.get(`${question.questionId}_comment`)?.value || null;

        if (answer) {
          answers.push({
            questionId: question.questionId,
            answer: answer,
            comment: comment
          });
        }
      });

      const submitRequest: InstantSurveySubmitRequest = {
        surveyId: this.activeSurvey.surveyId,
        surveyPosition: 'INSTANT_SURVEY_MAIN',
        answers: answers
      };

      this.store.dispatch(new SubmitInstantSurveyAction(submitRequest)).subscribe(() => {
        // Handle success response
        this.handleSubmitSuccess();
      });
    }
  }

  handleSubmitSuccess() {
    this.submitResponse$.subscribe(response => {
      if (response) {
        if (response.correctAnswers > 0) {
          this.successMessage = this.translateService.instant('_survey_success_message', {
            gainedBoomCoin: response.gainedBoomCoin
          });
        } else if (response.wrongAnswers > 0) {
          this.successMessage = this.translateService.instant('_survey_error_message');
        }
        this.showSuccessModal = true;
      }
    }).unsubscribe();
  }

  closeSuccessModal() {
    this.showSuccessModal = false;
    // Navigate back to list after closing modal
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }

  getModalIconClass(): string {
    // Check if we have a response to determine the icon
    let response: InstantSurveySubmitResponse = null;
    this.submitResponse$.subscribe(res => response = res).unsubscribe();

    if (response && response.correctAnswers > 0) {
      return 'icon icon-message-success'; // Green success icon
    } else {
      return 'icon icon-close-circle text-warning'; // Orange warning icon for incorrect answers
    }
  }

  goBack() {
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }

  openVisionLink() {
    this.frameService.sendMessage(FrameMessageEnum.openInPCC, {
      title: this.translateService.instant('Cat® VisionLink'),
      url: SSORedirectKeys.VisionLink,
      direct: true,
      pccDigitalBanko: false
    });
  }
}
