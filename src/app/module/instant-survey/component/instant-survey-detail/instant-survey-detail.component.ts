import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { InstantSurveyModel, InstantSurveySubmitRequest, InstantSurveyAnswer } from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { SubmitInstantSurveyAction } from '../../action/instant-survey.actions';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-instant-survey-detail',
  templateUrl: './instant-survey-detail.component.html',
  styleUrls: ['./instant-survey-detail.component.scss']
})
export class InstantSurveyDetailComponent implements OnInit {
  @Select(InstantSurveyState.activeSurvey)
  activeSurvey$: Observable<InstantSurveyModel>;

  @Select(InstantSurveyState.submitLoading)
  submitLoading$: Observable<boolean>;

  surveyForm: FormGroup;
  activeSurvey: InstantSurveyModel;
  surveyId: string;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store,
    private fb: FormBuilder
  ) { }

  ngOnInit() {
    this.surveyId = this.route.snapshot.paramMap.get('id');
    
    this.activeSurvey$.subscribe(survey => {
      if (survey && survey.surveyId === this.surveyId) {
        this.activeSurvey = survey;
        this.buildForm();
      } else if (!survey) {
        // If no active survey, redirect back to list
        this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
      }
    });
  }

  buildForm() {
    if (!this.activeSurvey || !this.activeSurvey.surveyQuestions) {
      return;
    }

    const formControls = {};
    
    this.activeSurvey.surveyQuestions.forEach(question => {
      const validators = question.isRequired ? [Validators.required] : [];
      formControls[question.questionId] = ['', validators];
      
      if (question.isCommentBoxEnabled) {
        const commentValidators = question.isCommentBoxRequired ? [Validators.required] : [];
        formControls[`${question.questionId}_comment`] = ['', commentValidators];
      }
    });

    this.surveyForm = this.fb.group(formControls);
  }

  onSubmit() {
    if (this.surveyForm.valid) {
      const answers: InstantSurveyAnswer[] = [];

      this.activeSurvey.surveyQuestions.forEach(question => {
        const answer = this.surveyForm.get(question.questionId)?.value;
        const comment = this.surveyForm.get(`${question.questionId}_comment`)?.value || null;

        if (answer) {
          answers.push({
            QuestionId: question.questionId,
            Answer: answer,
            Comment: comment
          });
        }
      });

      const submitRequest: InstantSurveySubmitRequest = {
        SurveyId: this.activeSurvey.surveyId,
        SurveyPosition: "INSTANT_SURVEY_MAIN",
        AdditionalData: null,
        RelatedContextId: null,
        Answers: answers
      };

      this.store.dispatch(new SubmitInstantSurveyAction(submitRequest)).subscribe(() => {
        // Navigate back to list after successful submission
        this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
      });
    }
  }

  goBack() {
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }
}
