<div class="instant-survey-list p-3">
  <div class="h4 py-2 mb-0 text-center">{{ "_instant_survey_list" | translate }}</div>

  <p class="text-justify px-2">
    {{ "_instant_survey_list_description" | translate }}
  </p>

  <div *ngFor="let item of surveyList" class="flex flex-column">
    <button
      class="btn p-4 mb-2 w-100 border-bottom d-flex justify-content-between align-items-start"
      style="border-radius: 0"
      (click)="openSurvey(item)"
    >
      <div class="d-flex flex-column text-left">
        <span class="font-weight-bold">{{ item.name }}</span>
        <span class="text-muted small mt-1">{{ item.description }}</span>
      </div>
      <div class="d-flex align-items-center">
        <i class="fas fa-chevron-right text-muted"></i>
      </div>
    </button>
  </div>

  <div *ngIf="(surveyLoading$ | async)" class="text-center py-4">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div *ngIf="!(surveyLoading$ | async) && surveyList.length === 0" class="text-center py-4">
    <p class="text-muted">{{ "_no_surveys_available" | translate }}</p>
  </div>
</div>
