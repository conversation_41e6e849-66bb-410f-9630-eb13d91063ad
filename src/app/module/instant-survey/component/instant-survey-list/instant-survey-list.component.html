<div class="instant-survey-list">
  <!-- Header Section -->
  <div class="header-section p-3 mb-3">
    <div class="h4 py-2 mb-0 text-center">{{ "_instant_survey_list" | translate }}</div>
    <p class="text-justify px-2">
      {{ "_instant_survey_list_description" | translate }}
    </p>
  </div>

  <!-- VisionLink Banner -->
  <div class="visionlink-banner mb-4 mx-3">
    <div class="card border-0 shadow-sm bg-warning text-white">
      <div class="card-body p-3">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <div class="visionlink-icon mr-3">
              <img src="/assets/badges/cat-app.svg" alt="Cat® App" class="cat-app-icon"/>
            </div>
            <div>
              <h6 class="mb-1 font-weight-bold">Cat® VisionLink</h6>
              <small class="text-dark-50">{{ "_access_visionlink_platform" | translate }}</small>
            </div>
          </div>
          <button class="btn btn-dark btn-sm" (click)="openVisionLink()">
            <i class="fas fa-arrow-right mr-1"></i>
            {{ "_open" | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Survey List -->
  <div class="survey-cards px-3">
    <div *ngFor="let item of surveyList; let i = index" class="survey-card mb-3">
      <div class="card border-0 shadow-sm h-100" (click)="openSurvey(item)">
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-start">
            <div class="survey-content flex-grow-1">
              <div class="d-flex align-items-center mb-2">
                <div class="survey-number bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mr-3">
                  {{ i + 1 }}
                </div>
                <h6 class="mb-0 font-weight-bold text-dark">{{ item.name }}</h6>
              </div>
              <p class="text-muted mb-2 small">{{ item.description }}</p>
              <div class="survey-meta d-flex align-items-center">
                <span class="badge badge-secondary mr-2">
                  <i class="fas fa-question-circle mr-1"></i>
                  {{ item.surveyQuestions?.length || 0 }} {{ "_questions" | translate }}
                </span>
                <span class="badge badge-warning text-dark" *ngIf="item.isActive">
                  <i class="fas fa-check-circle mr-1"></i>
                  {{ "_active" | translate }}
                </span>
              </div>
            </div>
            <div class="survey-arrow d-flex align-items-center">
              <i class="fas fa-chevron-right text-warning fa-lg"></i>
            </div>
          </div>
        </div>
        <div class="card-footer bg-transparent border-0 pt-0">
          <div class="progress" style="height: 3px;">
            <div class="progress-bar bg-primary" role="progressbar" style="width: 0%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="(surveyLoading$ | async)" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="text-muted mt-3">{{ "_loading_surveys" | translate }}</p>
  </div>

  <!-- Empty State -->
  <div *ngIf="!(surveyLoading$ | async) && surveyList.length === 0" class="empty-state text-center py-5 mx-3">
    <div class="empty-icon mb-3">
      <i class="fas fa-clipboard-list fa-3x text-muted"></i>
    </div>
    <h5 class="text-muted mb-2">{{ "_no_surveys_available" | translate }}</h5>
    <p class="text-muted small">{{ "_check_back_later" | translate }}</p>
  </div>
</div>
