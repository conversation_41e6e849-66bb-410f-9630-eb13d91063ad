import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { InstantSurveyModel } from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { GetInstantSurveysAction, SetActiveInstantSurveyAction } from '../../action/instant-survey.actions';
import { environment } from '../../../../../environments/environment';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'cat-instant-survey-list',
  templateUrl: './instant-survey-list.component.html',
  styleUrls: ['./instant-survey-list.component.scss']
})
export class InstantSurveyListComponent implements OnInit {
  @Select(InstantSurveyState.surveyList)
  surveyList$: Observable<InstantSurveyModel[]>;

  @Select(InstantSurveyState.surveyLoading)
  surveyLoading$: Observable<boolean>;

  surveyList: InstantSurveyModel[] = [];

  constructor(
    private store: Store,
    private router: Router,
    private frameService: MessageFrameService,
    private translateService: TranslateService
  ) { }

  ngOnInit() {
    this.store.dispatch(new GetInstantSurveysAction());

    this.surveyList$.subscribe(item => {
      if (item) {
        this.surveyList = item;
      }
    });
  }

  openSurvey(survey: InstantSurveyModel) {
    this.store.dispatch(new SetActiveInstantSurveyAction(survey));

    this.router.navigate([
      ...environment.rootUrl.split('/'),
      'instant-survey',
      'detail',
      survey.surveyId
    ]);
  }

  openVisionLink() {
    this.frameService.sendMessage(FrameMessageEnum.openInPCC, {
      title: this.translateService.instant('Cat® VisionLink'),
      url: SSORedirectKeys.VisionLink,
      direct: true,
      pccDigitalBanko: false
    });
  }
}
