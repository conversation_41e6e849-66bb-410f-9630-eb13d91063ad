.instant-survey-list {
  background-color: #f8f9fa;
  min-height: 100vh;

  // Header Section
  .header-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);

    .opacity-90 {
      opacity: 0.9;
    }
  }

  // VisionLink Banner
  .visionlink-banner {
    .bg-gradient-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .card {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3) !important;
      }
    }

    .visionlink-icon {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .btn-light {
      border: none;
      font-weight: 600;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
        transform: scale(1.05);
      }
    }
  }

  // Survey Cards
  .survey-cards {
    .survey-card {
      .card {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;

          .survey-arrow i {
            transform: translateX(5px);
            color: #0056b3 !important;
          }

          .progress-bar {
            width: 100% !important;
          }
        }
      }

      .survey-number {
        width: 32px;
        height: 32px;
        font-size: 14px;
        font-weight: 600;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      }

      .survey-content {
        h6 {
          color: #2c3e50;
          line-height: 1.4;
        }

        .text-muted {
          color: #6c757d !important;
          line-height: 1.5;
        }
      }

      .survey-meta {
        .badge {
          font-size: 11px;
          padding: 4px 8px;
          border-radius: 6px;

          &.badge-light {
            background-color: #e9ecef;
            color: #495057;
          }

          &.badge-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          }
        }
      }

      .survey-arrow {
        i {
          transition: all 0.3s ease;
        }
      }

      .progress {
        border-radius: 0;
        background-color: #e9ecef;

        .progress-bar {
          transition: width 0.5s ease;
          background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
        }
      }
    }
  }

  // Loading State
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  // Empty State
  .empty-state {
    .empty-icon {
      opacity: 0.6;
    }

    h5 {
      font-weight: 600;
    }
  }

  // Responsive Design
  @media (max-width: 576px) {
    .header-section {
      border-radius: 0 0 15px 15px;
      padding: 1.5rem !important;

      h4 {
        font-size: 1.25rem;
      }
    }

    .survey-card .card-body {
      padding: 1rem !important;
    }

    .survey-number {
      width: 28px !important;
      height: 28px !important;
      font-size: 12px !important;
    }
  }
}
