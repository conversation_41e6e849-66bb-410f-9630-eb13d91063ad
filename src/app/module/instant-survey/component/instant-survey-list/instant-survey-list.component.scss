.instant-survey-list {
  background-color: #f8f9fa;
  min-height: 100vh;

  // Header Section
  .header-section {
    // Simple header without special styling
  }

  // VisionLink Banner
  .visionlink-banner {
    .card {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3) !important;
      }
    }

    .visionlink-icon {
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .text-dark-50 {
      opacity: 0.7;
    }

    .btn-dark {
      border: none;
      font-weight: 600;
      transition: all 0.2s ease;

      &:hover {
        background-color: #343a40;
        transform: scale(1.05);
      }
    }
  }

  // Survey Cards
  .survey-cards {
    .survey-card {
      .card {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;

          .survey-arrow i {
            transform: translateX(5px);
            color: #e0a800 !important;
          }

          .progress-bar {
            width: 100% !important;
          }
        }
      }

      .survey-number {
        width: 32px;
        height: 32px;
        font-size: 14px;
        font-weight: 600;
        background-color: #6c757d;
      }

      .survey-content {
        h6 {
          color: #2c3e50;
          line-height: 1.4;
        }

        .text-muted {
          color: #6c757d !important;
          line-height: 1.5;
        }
      }

      .survey-meta {
        .badge {
          font-size: 11px;
          padding: 4px 8px;
          border-radius: 4px;

          &.badge-secondary {
            background-color: #6c757d;
            color: white;
          }

          &.badge-warning {
            background-color: #ffc107;
            color: #212529;
          }
        }
      }

      .survey-arrow {
        i {
          transition: all 0.3s ease;
        }
      }

      .progress {
        border-radius: 0;
        background-color: #e9ecef;

        .progress-bar {
          transition: width 0.5s ease;
          background-color: #ffc107;
        }
      }
    }
  }

  // Loading State
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  // Empty State
  .empty-state {
    .empty-icon {
      opacity: 0.6;
    }

    h5 {
      font-weight: 600;
    }
  }

  // Responsive Design
  @media (max-width: 576px) {
    .header-section {
      border-radius: 0 0 15px 15px;
      padding: 1.5rem !important;

      h4 {
        font-size: 1.25rem;
      }
    }

    .survey-card .card-body {
      padding: 1rem !important;
    }

    .survey-number {
      width: 28px !important;
      height: 28px !important;
      font-size: 12px !important;
    }
  }
}
