import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { InstantSurveyModel } from '../model/instant-survey.model';
import { InstantSurveyService } from '../service/instant-survey.service';
import {
  GetInstantSurveysAction,
  SetActiveInstantSurveyAction,
  SubmitInstantSurveyAction
} from '../action/instant-survey.actions';

export interface InstantSurveyStateModel {
  surveyList: InstantSurveyModel[];
  surveyLoading: boolean;
  activeSurvey: InstantSurveyModel;
  submitLoading: boolean;
}

@State<InstantSurveyStateModel>({
  name: 'instantSurvey',
  defaults: {
    surveyList: [],
    surveyLoading: false,
    activeSurvey: null,
    submitLoading: false,
  }
})
@Injectable()
export class InstantSurveyState {

  constructor(private instantSurveyService: InstantSurveyService) {
  }

  @Selector()
  static surveyList(state: InstantSurveyStateModel): InstantSurveyModel[] {
    return state.surveyList;
  }

  @Selector()
  static surveyLoading(state: InstantSurveyStateModel): boolean {
    return state.surveyLoading;
  }

  @Selector()
  static activeSurvey(state: InstantSurveyStateModel): InstantSurveyModel {
    return state.activeSurvey;
  }

  @Selector()
  static submitLoading(state: InstantSurveyStateModel): boolean {
    return state.submitLoading;
  }

  @Action(GetInstantSurveysAction)
  getInstantSurveysAction(
    { patchState }: StateContext<InstantSurveyStateModel>,
    action: GetInstantSurveysAction,
  ) {
    patchState({ surveyLoading: true });
    return this.instantSurveyService.getList()
      .pipe(
        tap(list => {
          patchState({
            surveyList: list || [],
            surveyLoading: false
          });
        }),
        catchError((err) => {
          patchState({ surveyLoading: false });
          return throwError(err);
        })
      );
  }

  @Action(SetActiveInstantSurveyAction)
  setActiveInstantSurveyAction(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { survey }: SetActiveInstantSurveyAction,
  ) {
    patchState({ activeSurvey: survey });
  }

  @Action(SubmitInstantSurveyAction)
  submitInstantSurveyAction(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { request }: SubmitInstantSurveyAction,
  ) {
    patchState({ submitLoading: true });
    return this.instantSurveyService.submitSurvey(request)
      .pipe(
        tap(result => {
          patchState({ submitLoading: false });
        }),
        catchError((err) => {
          patchState({ submitLoading: false });
          return throwError(err);
        })
      );
  }
}
