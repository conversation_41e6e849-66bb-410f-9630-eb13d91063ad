import { InstantSurveyModel, InstantSurveySubmitRequest } from '../model/instant-survey.model';

export class GetInstantSurveysAction {
  public static readonly type = '[InstantSurvey] Get Instant Survey List';

  constructor() {
  }
}

export class SetActiveInstantSurveyAction {
  public static readonly type = '[InstantSurvey] Set Active Survey';

  constructor(public survey: InstantSurveyModel) {
  }
}

export class SubmitInstantSurveyAction {
  public static readonly type = '[InstantSurvey] Submit Survey';

  constructor(public request: InstantSurveySubmitRequest) {
  }
}
