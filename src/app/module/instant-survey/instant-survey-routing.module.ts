import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { InstantSurveyLayoutComponent } from './component/instant-survey-layout/instant-survey-layout.component';
import { InstantSurveyListComponent } from './component/instant-survey-list/instant-survey-list.component';
import { InstantSurveyDetailComponent } from './component/instant-survey-detail/instant-survey-detail.component';

const routes: Routes = [
  {
    path: '',
    component: InstantSurveyLayoutComponent,
    children: [
      { path: '', component: InstantSurveyListComponent },
      { path: 'detail/:id', component: InstantSurveyDetailComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  declarations: [],
})
export class InstantSurveyRoutingModule {
  constructor() {
    stopLoadingAnimation();
  }
}
